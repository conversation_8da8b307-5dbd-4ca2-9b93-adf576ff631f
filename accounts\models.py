from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    ROLE_CHOICES = (
        ('manager', 'مدير'),
        ('sales', 'موظف مبيعات'),
        ('accountant', 'محاسب'),
    )
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, verbose_name='الدور')
    phone = models.CharField(max_length=15, blank=True, verbose_name='الهاتف')
    
    def __str__(self):
        return self.username
