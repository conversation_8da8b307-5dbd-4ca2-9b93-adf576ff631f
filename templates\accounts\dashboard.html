<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-info span {
            margin-left: 10px;
        }
        .container {
            padding: 20px;
        }
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
        }
        .card h2 {
            margin-top: 0;
            color: #333;
        }
        .card .value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .nav {
            background-color: #333;
            padding: 10px 0;
        }
        .nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            justify-content: center;
        }
        .nav li {
            margin: 0 15px;
        }
        .nav a {
            color: white;
            text-decoration: none;
            font-weight: bold;
        }
        .nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>نظام إدارة الفواتير</h1>
        <div class="user-info">
            <span>{{ username }} ({{ role }})</span>
            <a href="{% url 'accounts:logout' %}" style="color: white; margin-right: 15px;">تسجيل الخروج</a>
        </div>
    </div>

    <div class="nav">
        <ul>
            <li><a href="#">الرئيسية</a></li>
            <li><a href="#">إدارة المنتجات</a></li>
            <li><a href="#">إدارة العملاء</a></li>
            <li><a href="#">الفواتير</a></li>
            <li><a href="#">التقارير</a></li>
            <li><a href="#">الإعدادات</a></li>
        </ul>
    </div>

    <div class="container">
        <h2>مرحبا بك في لوحة التحكم</h2>
        
        <div class="dashboard-cards">
            <div class="card">
                <h2>إجمالي المبيعات اليوم</h2>
                <div class="value">0 ر.ق</div>
            </div>
            <div class="card">
                <h2>عدد الفواتير</h2>
                <div class="value">0</div>
            </div>
            <div class="card">
                <h2>عدد العملاء</h2>
                <div class="value">0</div>
            </div>
            <div class="card">
                <h2>حركة المخزون</h2>
                <div class="value">0</div>
            </div>
        </div>
    </div>
</body>
</html>